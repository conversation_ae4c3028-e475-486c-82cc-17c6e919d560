using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// GPU-accelerated geometry-based renderer for MIDI notes using Raylib vertex buffers
    /// This replaces the CPU-based DrawRectangle approach with batched GPU rendering
    /// </summary>
    public class RaylibGeometryRenderer : IDisposable
    {
        [StructLayout(LayoutKind.Sequential)]
        public struct NoteVertex
        {
            public float x, y, z;       // Position (3 floats)
            public float r, g, b, a;    // Color (4 floats)
            public float u, v;          // Texture coordinates (2 floats) - unused but required for compatibility

            public NoteVertex(float x, float y, float z, RaylibColor color)
            {
                this.x = x;
                this.y = y;
                this.z = z;
                this.r = color.r / 255.0f;
                this.g = color.g / 255.0f;
                this.b = color.b / 255.0f;
                this.a = color.a / 255.0f;
                this.u = 0.0f;
                this.v = 0.0f;
            }
        }

        private const int MAX_NOTES = 50000;  // Maximum notes that can be rendered in one batch
        private const int VERTICES_PER_NOTE = 12; // 2 triangles per note layer (shadow + inner) = 4 triangles = 12 vertices
        private const int MAX_VERTICES = MAX_NOTES * VERTICES_PER_NOTE;

        private List<NoteVertex> vertexBuffer;
        private uint vaoId;
        private uint vboId;
        private Material defaultMaterial;
        private bool isInitialized;

        private int screenWidth;
        private int screenHeight;
        private float keyboardHeight;

        public RaylibGeometryRenderer()
        {
            vertexBuffer = new List<NoteVertex>(MAX_VERTICES);
            isInitialized = false;
        }

        public void Initialize(int screenWidth, int screenHeight)
        {
            this.screenWidth = screenWidth;
            this.screenHeight = screenHeight;
            this.keyboardHeight = screenHeight * 0.15f; // 15% for keyboard

            // Load default material
            defaultMaterial = Raylib.LoadMaterialDefault();

            // Create vertex array and buffer
            vaoId = Raylib.rlLoadVertexArray();
            
            // Create vertex buffer (dynamic for frequent updates)
            int vertexSize = Marshal.SizeOf<NoteVertex>();
            vboId = Raylib.rlLoadVertexBuffer(IntPtr.Zero, MAX_VERTICES * vertexSize, true);

            // Set up vertex attributes
            Raylib.rlEnableVertexArray(vaoId);
            
            // Position attribute (location 0): 3 floats at offset 0
            Raylib.rlSetVertexAttribute(0, 3, RL_FLOAT, false, vertexSize, 0);
            
            // Color attribute (location 1): 4 floats at offset 12 (3 * sizeof(float))
            Raylib.rlSetVertexAttribute(1, 4, RL_FLOAT, false, vertexSize, 12);
            
            // Texture coordinate attribute (location 2): 2 floats at offset 28 (7 * sizeof(float))
            Raylib.rlSetVertexAttribute(2, 2, RL_FLOAT, false, vertexSize, 28);

            Raylib.rlDisableVertexArray();

            isInitialized = true;
        }

        public void BeginFrame()
        {
            vertexBuffer.Clear();
        }

        public void AddNote(float left, float right, float start, float end, RaylibColor colorLeft, RaylibColor colorRight)
        {
            if (!isInitialized || vertexBuffer.Count >= MAX_VERTICES - VERTICES_PER_NOTE)
                return;

            float noteAreaHeight = screenHeight - keyboardHeight;

            // Convert normalized coordinates to screen coordinates
            float noteLeft = left * screenWidth;
            float noteRight = right * screenWidth;

            // Convert time coordinates to screen Y coordinates (flipped vertically)
            float noteTop = noteAreaHeight - (end * noteAreaHeight);
            float noteBottom = noteAreaHeight - (start * noteAreaHeight);

            // Ensure notes are rendered in the correct area
            if (noteTop < 0) noteTop = 0;
            if (noteBottom > noteAreaHeight) noteBottom = noteAreaHeight;
            if (noteTop >= noteBottom) return; // Skip invalid notes

            float width = noteRight - noteLeft;
            float height = noteBottom - noteTop;

            // Ensure minimum note height for visibility
            float minHeight = noteAreaHeight * 0.01f;
            if (height < minHeight)
            {
                height = minHeight;
                noteBottom = noteTop + height;
            }

            if (width <= 0 || height <= 0) return;

            // Calculate border thickness (matching original shader)
            float noteBorder = 0.00091f;
            float noteBorderH = (float)Math.Round(noteBorder * screenWidth) / screenWidth * screenWidth;
            float noteBorderV = (float)Math.Round(noteBorder * screenHeight) / screenHeight * screenHeight / (screenHeight / (float)screenWidth);

            // Ensure minimum border size
            noteBorderH = Math.Max(1, noteBorderH);
            noteBorderV = Math.Max(1, noteBorderV);

            // Generate shadow colors (matching shader: cl.xyz *= 0.2f; cl.xyz -= 0.05f;)
            RaylibColor shadowColorL = new RaylibColor(
                (byte)Math.Max(0, Math.Min(255, colorLeft.r * 0.2f - 13)),
                (byte)Math.Max(0, Math.Min(255, colorLeft.g * 0.2f - 13)),
                (byte)Math.Max(0, Math.Min(255, colorLeft.b * 0.2f - 13)),
                colorLeft.a
            );
            RaylibColor shadowColorR = new RaylibColor(
                (byte)Math.Max(0, Math.Min(255, colorRight.r * 0.2f - 13)),
                (byte)Math.Max(0, Math.Min(255, colorRight.g * 0.2f - 13)),
                (byte)Math.Max(0, Math.Min(255, colorRight.b * 0.2f - 13)),
                colorRight.a
            );

            // Add shadow background triangles (2 triangles = 6 vertices)
            AddQuadTriangles(noteLeft, noteTop, noteRight, noteBottom, shadowColorL, shadowColorR);

            // Generate inner colors (matching shader: cl.xyz += 0.1f; cr.xyz -= 0.3f;)
            float borderTop = noteTop + noteBorderV;
            float borderBottom = noteBottom - noteBorderV;
            float borderLeft = noteLeft + noteBorderH;
            float borderRight = noteRight - noteBorderH;

            // Check if there's enough space for inner area
            if (borderTop < borderBottom && borderLeft < borderRight)
            {
                RaylibColor innerColorL = new RaylibColor(
                    (byte)Math.Max(0, Math.Min(255, colorLeft.r + 25)),
                    (byte)Math.Max(0, Math.Min(255, colorLeft.g + 25)),
                    (byte)Math.Max(0, Math.Min(255, colorLeft.b + 25)),
                    colorLeft.a
                );
                RaylibColor innerColorR = new RaylibColor(
                    (byte)Math.Max(0, Math.Min(255, colorRight.r - 76)),
                    (byte)Math.Max(0, Math.Min(255, colorRight.g - 76)),
                    (byte)Math.Max(0, Math.Min(255, colorRight.b - 76)),
                    colorRight.a
                );

                // Add inner area triangles (2 triangles = 6 vertices)
                AddQuadTriangles(borderLeft, borderTop, borderRight, borderBottom, innerColorL, innerColorR);
            }
        }

        private void AddQuadTriangles(float left, float top, float right, float bottom, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // Convert screen coordinates to normalized device coordinates (-1 to 1)
            float x1 = (left / screenWidth) * 2.0f - 1.0f;
            float y1 = 1.0f - (top / screenHeight) * 2.0f;
            float x2 = (right / screenWidth) * 2.0f - 1.0f;
            float y2 = 1.0f - (bottom / screenHeight) * 2.0f;

            // First triangle: top-left, bottom-left, top-right
            vertexBuffer.Add(new NoteVertex(x1, y1, 0.0f, colorLeft));    // top-left
            vertexBuffer.Add(new NoteVertex(x1, y2, 0.0f, colorLeft));    // bottom-left
            vertexBuffer.Add(new NoteVertex(x2, y1, 0.0f, colorRight));   // top-right

            // Second triangle: bottom-left, bottom-right, top-right
            vertexBuffer.Add(new NoteVertex(x1, y2, 0.0f, colorLeft));    // bottom-left
            vertexBuffer.Add(new NoteVertex(x2, y2, 0.0f, colorRight));   // bottom-right
            vertexBuffer.Add(new NoteVertex(x2, y1, 0.0f, colorRight));   // top-right
        }

        public void RenderNotes()
        {
            if (!isInitialized || vertexBuffer.Count == 0)
                return;

            // Upload vertex data to GPU
            int vertexSize = Marshal.SizeOf<NoteVertex>();
            int dataSize = vertexBuffer.Count * vertexSize;
            
            // Pin the vertex buffer data
            GCHandle handle = GCHandle.Alloc(vertexBuffer.ToArray(), GCHandleType.Pinned);
            try
            {
                IntPtr dataPtr = handle.AddrOfPinnedObject();
                
                // Update vertex buffer
                Raylib.rlUpdateVertexBuffer(vboId, dataPtr, dataSize, 0);
                
                // Draw the vertices
                Raylib.rlEnableVertexArray(vaoId);
                Raylib.rlDrawVertexArray(0, vertexBuffer.Count);
                Raylib.rlDisableVertexArray();
            }
            finally
            {
                handle.Free();
            }
        }

        public void UpdateScreenSize(int width, int height)
        {
            screenWidth = width;
            screenHeight = height;
            keyboardHeight = height * 0.15f;
        }

        public void Dispose()
        {
            if (isInitialized)
            {
                Raylib.rlUnloadVertexBuffer(vboId);
                Raylib.rlUnloadVertexArray(vaoId);
                Raylib.UnloadMaterial(defaultMaterial);
                isInitialized = false;
            }
        }
    }
}
