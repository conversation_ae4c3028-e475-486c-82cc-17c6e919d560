using System;
using System.Runtime.InteropServices;

namespace Kiva_MIDI
{
    /// <summary>
    /// Direct P/Invoke bindings for raylib - simplified version for basic functionality
    /// This avoids the .NET Standard compatibility issues with Raylib-cs
    /// </summary>
    public static class RaylibPInvoke
    {
        private const string RAYLIB_DLL = "raylib.dll";

        [StructLayout(LayoutKind.Sequential)]
        public struct Color
        {
            public byte r;
            public byte g;
            public byte b;
            public byte a;

            public Color(byte r, byte g, byte b, byte a)
            {
                this.r = r;
                this.g = g;
                this.b = b;
                this.a = a;
            }

            public static readonly Color BLACK = new Color(0, 0, 0, 255);
            public static readonly Color WHITE = new Color(255, 255, 255, 255);
            public static readonly Color RED = new Color(255, 0, 0, 255);
            public static readonly Color GREEN = new Color(0, 255, 0, 255);
            public static readonly Color BLUE = new Color(0, 0, 255, 255);
            public static readonly Color YELLOW = new Color(255, 255, 0, 255);
            public static readonly Color GRAY = new Color(128, 128, 128, 255);
            public static readonly Color DARKGRAY = new Color(80, 80, 80, 255);
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Vector2
        {
            public float x;
            public float y;

            public Vector2(float x, float y)
            {
                this.x = x;
                this.y = y;
            }
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Font
        {
            public int baseSize;
            public int glyphCount;
            public int glyphPadding;
            public IntPtr texture;
            public IntPtr recs;
            public IntPtr glyphs;
        }

        [Flags]
        public enum ConfigFlags
        {
            FLAG_VSYNC_HINT = 0x00000040,
            FLAG_FULLSCREEN_MODE = 0x00000002,
            FLAG_WINDOW_RESIZABLE = 0x00000004,
            FLAG_WINDOW_UNDECORATED = 0x00000008,
            FLAG_WINDOW_HIDDEN = 0x00000080,
            FLAG_WINDOW_MINIMIZED = 0x00000200,
            FLAG_WINDOW_MAXIMIZED = 0x00000400,
            FLAG_WINDOW_UNFOCUSED = 0x00000800,
            FLAG_WINDOW_TOPMOST = 0x00001000,
            FLAG_WINDOW_ALWAYS_RUN = 0x00000100,
            FLAG_WINDOW_TRANSPARENT = 0x00000010,
            FLAG_WINDOW_HIGHDPI = 0x00002000,
            FLAG_MSAA_4X_HINT = 0x00000020,
            FLAG_INTERLACED_HINT = 0x00010000
        }

        // Window management
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetConfigFlags(ConfigFlags flags);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void InitWindow(int width, int height, string title);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ToggleFullscreen();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsWindowFullscreen();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMonitorWidth(int monitor);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMonitorHeight(int monitor);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool WindowShouldClose();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void CloseWindow();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsWindowReady();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetTargetFPS(int fps);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetScreenWidth();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetScreenHeight();

        // Drawing
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void BeginDrawing();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void EndDrawing();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ClearBackground(Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawRectangle(int posX, int posY, int width, int height, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawRectangleLines(int posX, int posY, int width, int height, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawLine(int startPosX, int startPosY, int endPosX, int endPosY, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawCircle(int centerX, int centerY, float radius, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawCircleLines(int centerX, int centerY, float radius, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawText(string text, int posX, int posY, int fontSize, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern Font LoadFont(string fileName);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawTextEx(Font font, string text, Vector2 position, float fontSize, float spacing, Color tint);

        // Input
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsKeyPressed(int key);



        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsKeyDown(int key);

        // Mouse input
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonPressed(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonDown(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonReleased(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMouseX();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMouseY();

        // Key codes
        public const int KEY_ESCAPE = 256;
        public const int KEY_SPACE = 32;
        public const int KEY_ENTER = 257;
        public const int KEY_BACKSPACE = 259;
        public const int KEY_DELETE = 261;
        public const int KEY_TAB = 258;

        // Arrow keys
        public const int KEY_UP = 265;
        public const int KEY_DOWN = 264;
        public const int KEY_LEFT = 263;
        public const int KEY_RIGHT = 262;

        // Modifier keys
        public const int KEY_LEFT_SHIFT = 340;
        public const int KEY_RIGHT_SHIFT = 344;

        // Letter keys (ASCII values)
        public const int KEY_G = 71;
        public const int KEY_H = 72;
        public const int KEY_I = 73;
        public const int KEY_O = 79;
        public const int KEY_P = 80;
        public const int KEY_S = 83;

        // Number keys (ASCII values)
        public const int KEY_0 = 48;
        public const int KEY_1 = 49;
        public const int KEY_2 = 50;
        public const int KEY_3 = 51;
        public const int KEY_4 = 52;
        public const int KEY_5 = 53;
        public const int KEY_6 = 54;
        public const int KEY_7 = 55;
        public const int KEY_8 = 56;
        public const int KEY_9 = 57;

        // Special characters
        public const int KEY_PERIOD = 46; // '.' key

        // Mouse buttons
        public const int MOUSE_BUTTON_LEFT = 0;
        public const int MOUSE_BUTTON_RIGHT = 1;
        public const int MOUSE_BUTTON_MIDDLE = 2;

        // Mesh and Material structures
        [StructLayout(LayoutKind.Sequential)]
        public struct Mesh
        {
            public int vertexCount;
            public int triangleCount;
            public IntPtr vertices;     // float*
            public IntPtr texcoords;    // float*
            public IntPtr texcoords2;   // float*
            public IntPtr normals;      // float*
            public IntPtr tangents;     // float*
            public IntPtr colors;       // unsigned char*
            public IntPtr indices;      // unsigned short*
            public IntPtr animVertices; // float*
            public IntPtr animNormals;  // float*
            public IntPtr boneIds;      // unsigned char*
            public IntPtr boneWeights;  // float*
            public uint vaoId;          // OpenGL Vertex Array Object id
            public IntPtr vboId;        // OpenGL Vertex Buffer Objects id (default vertex data)
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Material
        {
            public IntPtr shader;       // Material shader
            public IntPtr maps;         // Material maps array (MAX_MATERIAL_MAPS)
            public IntPtr @params;      // Material generic parameters (if required)
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Matrix
        {
            public float m0, m4, m8, m12;
            public float m1, m5, m9, m13;
            public float m2, m6, m10, m14;
            public float m3, m7, m11, m15;
        }

        // Mesh and vertex buffer functions for GPU-accelerated rendering
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UploadMesh(ref Mesh mesh, bool dynamic);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UpdateMeshBuffer(Mesh mesh, int index, IntPtr data, int dataSize, int offset);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawMesh(Mesh mesh, Material material, Matrix transform);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UnloadMesh(Mesh mesh);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern Material LoadMaterialDefault();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UnloadMaterial(Material material);

        // rlgl functions for low-level vertex buffer operations
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlBegin(int mode);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlEnd();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlVertex3f(float x, float y, float z);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlColor4f(float r, float g, float b, float a);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlTexCoord2f(float x, float y);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern uint rlLoadVertexBuffer(IntPtr buffer, int size, bool dynamic);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlUpdateVertexBuffer(uint bufferId, IntPtr data, int dataSize, int offset);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlUnloadVertexBuffer(uint vboId);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern uint rlLoadVertexArray();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlSetVertexAttribute(uint index, int compSize, int type, bool normalized, int stride, int offset);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool rlEnableVertexArray(uint vaoId);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlDisableVertexArray();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlDrawVertexArray(int offset, int count);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void rlUnloadVertexArray(uint vaoId);

        // Constants for rlgl
        public const int RL_TRIANGLES = 0x0004;
        public const int RL_FLOAT = 0x1406;

        // Helper methods for easier usage
        public static class Raylib
        {
            public static void InitWindow(int width, int height, string title) => RaylibPInvoke.InitWindow(width, height, title);
            public static bool WindowShouldClose() => RaylibPInvoke.WindowShouldClose();
            public static void CloseWindow() => RaylibPInvoke.CloseWindow();
            public static bool IsWindowReady() => RaylibPInvoke.IsWindowReady();
            public static void SetTargetFPS(int fps) => RaylibPInvoke.SetTargetFPS(fps);
            public static int GetScreenWidth() => RaylibPInvoke.GetScreenWidth();
            public static int GetScreenHeight() => RaylibPInvoke.GetScreenHeight();
            public static void BeginDrawing() => RaylibPInvoke.BeginDrawing();
            public static void EndDrawing() => RaylibPInvoke.EndDrawing();
            public static void ClearBackground(Color color) => RaylibPInvoke.ClearBackground(color);
            public static void DrawRectangle(int posX, int posY, int width, int height, Color color) => RaylibPInvoke.DrawRectangle(posX, posY, width, height, color);
            public static void DrawRectangleLines(int posX, int posY, int width, int height, Color color) => RaylibPInvoke.DrawRectangleLines(posX, posY, width, height, color);
            public static void DrawLine(int startPosX, int startPosY, int endPosX, int endPosY, Color color) => RaylibPInvoke.DrawLine(startPosX, startPosY, endPosX, endPosY, color);
            public static void DrawCircle(int centerX, int centerY, float radius, Color color) => RaylibPInvoke.DrawCircle(centerX, centerY, radius, color);
            public static void DrawCircleLines(int centerX, int centerY, float radius, Color color) => RaylibPInvoke.DrawCircleLines(centerX, centerY, radius, color);
            public static void DrawText(string text, int posX, int posY, int fontSize, Color color) => RaylibPInvoke.DrawText(text, posX, posY, fontSize, color);
            public static Font LoadFont(string fileName) => RaylibPInvoke.LoadFont(fileName);
            public static void DrawTextEx(Font font, string text, Vector2 position, float fontSize, float spacing, Color tint) => RaylibPInvoke.DrawTextEx(font, text, position, fontSize, spacing, tint);
            public static bool IsKeyPressed(int key) => RaylibPInvoke.IsKeyPressed(key);
            public static bool IsKeyDown(int key) => RaylibPInvoke.IsKeyDown(key);
            public static bool IsMouseButtonPressed(int button) => RaylibPInvoke.IsMouseButtonPressed(button);
            public static bool IsMouseButtonDown(int button) => RaylibPInvoke.IsMouseButtonDown(button);
            public static bool IsMouseButtonReleased(int button) => RaylibPInvoke.IsMouseButtonReleased(button);
            public static int GetMouseX() => RaylibPInvoke.GetMouseX();
            public static int GetMouseY() => RaylibPInvoke.GetMouseY();
            public static void SetConfigFlags(ConfigFlags flags) => RaylibPInvoke.SetConfigFlags(flags);
            public static void ToggleFullscreen() => RaylibPInvoke.ToggleFullscreen();
            public static bool IsWindowFullscreen() => RaylibPInvoke.IsWindowFullscreen();
            public static int GetMonitorWidth(int monitor) => RaylibPInvoke.GetMonitorWidth(monitor);
            public static int GetMonitorHeight(int monitor) => RaylibPInvoke.GetMonitorHeight(monitor);

            // Mesh and vertex buffer helper methods
            public static void UploadMesh(ref Mesh mesh, bool dynamic) => RaylibPInvoke.UploadMesh(ref mesh, dynamic);
            public static void UpdateMeshBuffer(Mesh mesh, int index, IntPtr data, int dataSize, int offset) => RaylibPInvoke.UpdateMeshBuffer(mesh, index, data, dataSize, offset);
            public static void DrawMesh(Mesh mesh, Material material, Matrix transform) => RaylibPInvoke.DrawMesh(mesh, material, transform);
            public static void UnloadMesh(Mesh mesh) => RaylibPInvoke.UnloadMesh(mesh);
            public static Material LoadMaterialDefault() => RaylibPInvoke.LoadMaterialDefault();
            public static void UnloadMaterial(Material material) => RaylibPInvoke.UnloadMaterial(material);

            // rlgl helper methods
            public static void rlBegin(int mode) => RaylibPInvoke.rlBegin(mode);
            public static void rlEnd() => RaylibPInvoke.rlEnd();
            public static void rlVertex3f(float x, float y, float z) => RaylibPInvoke.rlVertex3f(x, y, z);
            public static void rlColor4f(float r, float g, float b, float a) => RaylibPInvoke.rlColor4f(r, g, b, a);
            public static void rlTexCoord2f(float x, float y) => RaylibPInvoke.rlTexCoord2f(x, y);
            public static uint rlLoadVertexBuffer(IntPtr buffer, int size, bool dynamic) => RaylibPInvoke.rlLoadVertexBuffer(buffer, size, dynamic);
            public static void rlUpdateVertexBuffer(uint bufferId, IntPtr data, int dataSize, int offset) => RaylibPInvoke.rlUpdateVertexBuffer(bufferId, data, dataSize, offset);
            public static void rlUnloadVertexBuffer(uint vboId) => RaylibPInvoke.rlUnloadVertexBuffer(vboId);
            public static uint rlLoadVertexArray() => RaylibPInvoke.rlLoadVertexArray();
            public static void rlSetVertexAttribute(uint index, int compSize, int type, bool normalized, int stride, int offset) => RaylibPInvoke.rlSetVertexAttribute(index, compSize, type, normalized, stride, offset);
            public static bool rlEnableVertexArray(uint vaoId) => RaylibPInvoke.rlEnableVertexArray(vaoId);
            public static void rlDisableVertexArray() => RaylibPInvoke.rlDisableVertexArray();
            public static void rlDrawVertexArray(int offset, int count) => RaylibPInvoke.rlDrawVertexArray(offset, count);
            public static void rlUnloadVertexArray(uint vaoId) => RaylibPInvoke.rlUnloadVertexArray(vaoId);

            // Matrix helper - identity matrix
            public static Matrix MatrixIdentity()
            {
                return new Matrix
                {
                    m0 = 1.0f, m4 = 0.0f, m8 = 0.0f, m12 = 0.0f,
                    m1 = 0.0f, m5 = 1.0f, m9 = 0.0f, m13 = 0.0f,
                    m2 = 0.0f, m6 = 0.0f, m10 = 1.0f, m14 = 0.0f,
                    m3 = 0.0f, m7 = 0.0f, m11 = 0.0f, m15 = 1.0f
                };
            }
        }
    }
}
